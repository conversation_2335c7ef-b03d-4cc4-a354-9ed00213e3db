import { Send } from "@mui/icons-material";
import { Box, CircularProgress, IconButton, Paper, Typography } from "@mui/material";
import React from "react";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { ChatMessage } from "src/services/api_definitions/chat.service";
import ChatMessageItem from "./ChatMessageItem";

interface ChatAreaProps {
  messages: ChatMessage[];
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: () => void;
  isLoading: boolean;
  messagesEndRef?: React.RefObject<HTMLDivElement>;
  onQuestionClick: (question: string) => void;
}

export default function ChatArea({
  messages,
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  messagesEndRef,
  onQuestionClick,
}: ChatAreaProps) {
  // const [showFAQ, setShowFAQ] = useState(false);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey && input.trim() && !isLoading) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // const toggleFAQ = () => {
  //   setShowFAQ(!showFAQ);
  // };



  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
        position: "relative",
      }}
    >
      {/* Messages area */}
      <Box
        sx={{
          flexGrow: 1,
          overflow: "auto",
          overflowX: "hidden",
          display: "flex",
          flexDirection: "column",
          gap: 2,
          pt: { xs: 1, sm: 2 },
          pb: { xs: 2, sm: 4 },
          px: { xs: 2, sm: 3, md: 4 },
          minHeight: 0, // Important for flex scrolling
          scrollBehavior: "smooth",
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "rgba(0,0,0,0.2)",
            borderRadius: "3px",
            "&:hover": {
              background: "rgba(0,0,0,0.3)",
            },
          },
        }}
      >
        {messages.length === 0 ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              gap: 4,
              px: { xs: 2, sm: 4 },
            }}
          >
            <Box sx={{ textAlign: "center", mb: 4 }}>
              <Typography variant="h4" sx={{ mb: 2, fontWeight: 600, color: "primary.main" }}>
                👋 Hello! How can I help you today?
              </Typography>
              <Typography variant="body1" sx={{ color: "text.secondary", fontSize: "1.1rem", lineHeight: 1.6 }}>
                I'm your AI assistant, ready to help with HR questions and tasks
              </Typography>
            </Box>
            {/* <FrequentQuestions onQuestionClick={onQuestionClick} /> */}
          </Box>
        ) : (
          messages.map((message, index) => {
            // Check if this is the latest assistant message
            const isLatestAssistantMessage = message.role === "assistant" && index === messages.length - 1;

            return (
              <ChatMessageItem
                key={message.role + message.content}
                message={message}
                isLatestAssistantMessage={isLatestAssistantMessage}
                isLoading={isLoading}
                isNewMessage={isLoading && isLatestAssistantMessage}
              />
            );
          })
        )}

        {/* Only show loader if there are no messages or the last message is from the user */}
        {isLoading && messages.length > 0 && messages[messages.length - 1].role === "user" && (
          <Box sx={{ display: "flex", justifyContent: "center", my: 3 }}>
            <CircularProgress size={24} />
          </Box>
        )}

        {/* Invisible div for auto-scrolling */}
        <Box
          ref={messagesEndRef}
          sx={{
            height: 0,
            width: "100%",
            flexShrink: 0
          }}
        />
      </Box>

      {/* FAQ Section - shows when there are messages and user clicks the FAQ button */}
      {messages.length > 0 && (
        <>
          {/* <Collapse in={showFAQ}>
            <Box sx={{ mb: 2, px: 1 }}>
              <FrequentQuestions onQuestionClick={onQuestionClick} />
            </Box>
          </Collapse> */}

          {/* Floating Action Button to toggle FAQ */}
          {/* <Tooltip title={showFAQ ? "Hide frequent questions" : "Show frequent questions"} placement="left">
            <Fab
              size="medium"
              onClick={toggleFAQ}
              sx={{
                position: "absolute",
                bottom: 120,
                right: 20,
                bgcolor: "primary.main",
                color: "primary.contrastText",
                boxShadow: "0 8px 25px rgba(0,127,111,0.3)",
                transition: "all 0.3s ease",
                "&:hover": {
                  bgcolor: "primary.dark",
                  transform: "scale(1.1)",
                  boxShadow: "0 12px 35px rgba(0,127,111,0.4)",
                },
                "&:active": {
                  transform: "scale(0.95)",
                },
              }}
            >
              {showFAQ ? <Close /> : <QuestionAnswer />}
            </Fab>
          </Tooltip> */}
        </>
      )}

      {/* Input area - Fixed at bottom */}
      <Box
        sx={{
          flexShrink: 0,
          pt: { xs: 2, sm: 3 },
          pb: { xs: 2, sm: 3 },
          px: { xs: 2, sm: 3, md: 4 },
          background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
        }}
      >
        <Paper
          elevation={2}
          sx={{
            p: { xs: 1.5, sm: 2 },
            display: "flex",
            alignItems: "center",
            borderRadius: 4,
            width: "100%",
            background: "white",
            border: "1px solid",
            borderColor: "divider",
            boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
            "&:hover": {
              borderColor: "primary.main",
              boxShadow: "0 6px 25px rgba(0,127,111,0.15)",
            },
            "&:focus-within": {
              borderColor: "primary.main",
              boxShadow: "0 0 0 3px rgba(0,127,111,0.1)",
            },
          }}
        >
          <CustomTextField
            className="flex-grow border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            placeholder="Type your message here..."
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            fullWidth
            sx={{
              "& .MuiOutlinedInput-root": {
                border: "none",
                "& fieldset": {
                  border: "none",
                },
              },
              "& .MuiInputBase-input": {
                fontSize: "0.95rem",
                py: { xs: 1, sm: 1.25 },
              },
            }}
          />
          <IconButton
            onClick={handleSubmit}
            type="submit"
            disabled={!input.trim() || isLoading}
            sx={{
              bgcolor: input.trim() && !isLoading ? "primary.main" : "grey.300",
              color: input.trim() && !isLoading ? "primary.contrastText" : "grey.500",
              ml: 1,
              width: 44,
              height: 44,
              transition: "all 0.3s ease",
              "&:hover": {
                bgcolor: input.trim() && !isLoading ? "primary.dark" : "grey.400",
                transform: input.trim() && !isLoading ? "scale(1.05)" : "none",
              },
              "&:disabled": {
                bgcolor: "grey.200",
                color: "grey.400",
              },
            }}
          >
            <Send fontSize="medium" />
          </IconButton>
        </Paper>
      </Box>
    </Box>
  );
}
