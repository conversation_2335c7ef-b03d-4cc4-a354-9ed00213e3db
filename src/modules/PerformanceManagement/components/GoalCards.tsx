import { ArrowRightAltSharp, CalendarToday, InfoOutlined, Schedule } from "@mui/icons-material";
import { Box, Button, Card, IconButton, Paper, Tooltip, Typography } from "@mui/material";
// GoalCard.jsx
import React from "react";
import {
  PerformanceCardIconV1,
  PerformanceCardIconV2,
  PerformanceCardIconV3,
  PerformanceCardIconV4,
} from "src/assets/icons.svg";
import { formatPeriodRange, getDueDateDisplayText, getOverdueDetails } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import {
  getDueDateChipStyling,
  getDueStatusStyling,
  getOverdueChipStyling,
  getPeriodDisplayStyling,
} from "../utils/goalCardUtils";

interface GoalCardProps {
  quarter: string;
  status: string;
  isAddGoal: boolean;
  iconIndex: number;
  onAddGoalClick?: () => void;
  onViewAddedGoalClick?: () => void;
  buttonTitle: string;
  disableIcon?: boolean;
  reason?: string;
  isDisabled?: boolean;
  start_date?: string;
  end_date?: string;
  dueDate?: string;
}

const CardIconConfig = [PerformanceCardIconV1, PerformanceCardIconV2, PerformanceCardIconV3, PerformanceCardIconV4];

const GoalCard: React.FC<GoalCardProps> = ({
  quarter,
  status,
  isAddGoal,
  iconIndex,
  onAddGoalClick,
  onViewAddedGoalClick,
  buttonTitle = "",
  disableIcon = false,
  reason,
  isDisabled = false,
  start_date,
  end_date,
  dueDate,
}) => {
  const Icon = CardIconConfig[iconIndex];

  // Get styling based on due date status
  const dueStatusStyling = getDueStatusStyling(dueDate || "");

  // Format period range for display
  const periodText = start_date && end_date ? formatPeriodRange(start_date, end_date) : null;

  // Get due date display text
  const dueDateText = dueDate ? getDueDateDisplayText(dueDate) : null;
  const overdueDetails = dueDate
    ? getOverdueDetails(dueDate)
    : { isOverdue: false, overdueText: "", dueDateFormatted: "" };

  return (
    <Tooltip placement="top" title={isDisabled ? "Goal setting period is over for this performance cycle" : ""}>
      <Card
        component={Paper}
        elevation={3}
        sx={{
          width: "100%",
          minHeight: 300,
          borderRadius: "15px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          padding: "16px",
          marginBottom: "20px",
          position: "relative",
          opacity: isDisabled ? 0.5 : 1,
          ...dueStatusStyling.cardSx,
        }}
      >
        <Box display="flex" flexDirection="column" gap={1} sx={{ paddingRight: "50px" }}>
          <Typography variant="h6" component="div" gutterBottom>
            {quarter}
          </Typography>

          {/* Minimal Period Display */}
          {periodText && (
            <Box sx={getPeriodDisplayStyling()}>
              <CalendarToday sx={{ fontSize: 14 }} />
              <Typography variant="body2" sx={{ fontSize: "0.8rem" }}>
                {periodText}
              </Typography>
            </Box>
          )}

          {/* Status Display */}
          <Box display="flex" gap={1} alignItems="center">
            <Typography variant="body2" color="text.secondary">
              Status:{" "}
              <Typography component="span" sx={{ color: getStatusColors(status), fontWeight: 600 }}>
                {status}
              </Typography>
            </Typography>
            {status === "Sent Back" && (
              <Tooltip title={reason}>
                <IconButton>
                  <InfoOutlined color="error" fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          {/* Enhanced Due Date Display */}
          {dueDateText && (
            <>
              {overdueDetails.isOverdue ? (
                <Box sx={getOverdueChipStyling()}>
                  <Box display="flex" alignItems="center" gap={0.5}>
                    <Schedule sx={{ fontSize: 12, color: "#BF360C" }} />
                    <Typography variant="body2" sx={{ fontSize: "0.7rem", fontWeight: 600, color: "#BF360C" }}>
                      {overdueDetails.overdueText}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontSize: "0.65rem", color: "#757575", lineHeight: 1.2 }}>
                    {overdueDetails.dueDateFormatted}
                  </Typography>
                </Box>
              ) : (
                <Box sx={getDueDateChipStyling(dueDate || "")}>
                  <Schedule sx={{ fontSize: 12 }} />
                  <Typography variant="body2" sx={{ fontSize: "0.7rem", fontWeight: 500 }}>
                    {dueDateText}
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>
        <Box
          display="flex"
          justifyContent="flex-start"
          alignItems="flex-end"
          width="100%"
          sx={{ position: "relative" }}
        >
          {!isAddGoal ? (
            !disableIcon && (
              <IconButton disabled={isDisabled} onClick={onViewAddedGoalClick} sx={{ background: "#E6F2F1" }}>
                <ArrowRightAltSharp />
              </IconButton>
            )
          ) : (
            <Button disabled={isDisabled} variant="contained" onClick={onAddGoalClick}>
              {buttonTitle}
            </Button>
          )}
          <Box position="absolute" right={0} bottom={-6} sx={{ zIndex: 1 }}>
            <Icon />
          </Box>
        </Box>
      </Card>
    </Tooltip>
  );
};

export default GoalCard;
