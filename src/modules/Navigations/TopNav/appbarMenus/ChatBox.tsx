import { Box } from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { useChatMutation } from "src/customHooks/useChatMutation";
import ChatArea from "src/modules/Chat/components/ChatArea";
import { ChatMessage } from "src/services/api_definitions/chat.service";

// Define the TokenData type to match what we're storing in the cache
type TokenData = {
  tokens: string[];
  chat_id: string;
};

const EffiChat: React.FC = () => {
  const [chatId, setChatId] = useState<string | null>(null);
  const [userInput, setUserInput] = useState("");

  // Initialize history from session storage
  const [history, setHistory] = useState<ChatMessage[]>(() => {
    try {
      const CHAT_MESSAGES_KEY = "effi_chat_messages";
      const storedMessages = sessionStorage.getItem(CHAT_MESSAGES_KEY);
      return storedMessages ? JSON.parse(storedMessages) : [];
    } catch (error) {
      console.error("Error loading chat messages from session storage:", error);
      return [];
    }
  });

  // token data lives in React-Query cache
  const { data: tokenData = { tokens: [], chat_id: "" } } = useQuery<TokenData>({
    queryKey: ["chatTokens", chatId || "new"],
    staleTime: Infinity, // never refetch automatically
    enabled: true, // always enabled to catch first response
    initialData: { tokens: [], chat_id: "" },
  });

  // Also query the "new" key to catch the first response when chatId is null
  const { data: newTokenData } = useQuery<TokenData>({
    queryKey: ["chatTokens", "new"],
    staleTime: Infinity,
    enabled: !chatId, // only enabled when chatId is null
    initialData: { tokens: [], chat_id: "" },
  });

  // Combine the data from both queries
  const effectiveTokenData = chatId ? tokenData : newTokenData;

  const chatMutation = useChatMutation();

  // Watch for chat ID changes in the token data
  useEffect(() => {
    // Use effectiveTokenData to get the chat ID
    const currentTokenData = effectiveTokenData;

    if (currentTokenData.chat_id && (!chatId || chatId !== currentTokenData.chat_id)) {
      console.log("Setting chat ID from token data:", currentTokenData.chat_id);
      setChatId(currentTokenData.chat_id);
    }
  }, [effectiveTokenData.chat_id, chatId]);

  // every render, join tokens into the last assistant message
  useEffect(() => {
    // Use effectiveTokenData to get the tokens
    const currentTokenData = effectiveTokenData;

    if (!currentTokenData.tokens.length) return;

    // Log the token data for debugging
    console.log("Processing token data:", currentTokenData);

    const assistant = currentTokenData.tokens.join("");
    console.log("Joined assistant message:", assistant);

    setHistory((prev) => {
      const draft = [...prev];
      const last = draft[draft.length - 1];
      if (last?.role === "assistant") {
        console.log("Replacing last assistant message");
        draft.pop(); // overwrite
      } else {
        console.log("Adding new assistant message");
      }
      return [...draft, { role: "assistant", content: assistant }];
    });

    // Auto-scroll during streaming to keep up with the typing
    setTimeout(() => {
      if (messagesEndRef.current) {
        try {
          messagesEndRef.current.scrollIntoView({
            behavior: "smooth",
            block: "end",
            inline: "nearest"
          });
        } catch {
          messagesEndRef.current.scrollIntoView(false);
        }
      }
    }, 50);
  }, [effectiveTokenData.tokens]);

  // Add a ref to track the message container for auto-scrolling
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Get the query client
  const queryClient = useQueryClient();

  // Persist messages to session storage whenever history changes
  useEffect(() => {
    try {
      const CHAT_MESSAGES_KEY = "effi_chat_messages";
      sessionStorage.setItem(CHAT_MESSAGES_KEY, JSON.stringify(history));
    } catch (error) {
      console.error("Error saving chat messages to session storage:", error);
    }
  }, [history]);

  // Auto-scroll to the bottom when messages change
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        // Try multiple scroll methods for better compatibility
        try {
          messagesEndRef.current.scrollIntoView({
            behavior: "smooth",
            block: "end",
            inline: "nearest"
          });
        } catch {
          // Fallback for older browsers
          messagesEndRef.current.scrollIntoView(false);
        }

        // Additional fallback: scroll the parent container
        const scrollContainer = messagesEndRef.current.closest('[style*="overflow"]');
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      }
    };

    // Use a small delay to ensure content is rendered before scrolling
    const timeoutId = setTimeout(scrollToBottom, 150);

    return () => clearTimeout(timeoutId);
  }, [history]);

  const sendMessage = (customPrompt?: string) => {
    const prompt = customPrompt || userInput.trim();
    if (!prompt) return;

    setHistory((prev) => [...prev, { role: "user", content: prompt }]);
    setUserInput("");

    // Clear the tokens for the new response
    if (chatId) {
      // If we have a chat ID, clear the tokens for that chat
      queryClient.setQueryData<TokenData>(["chatTokens", chatId], (prev) => ({
        tokens: [],
        chat_id: prev?.chat_id || "",
      }));
    } else {
      // If we don't have a chat ID, clear the tokens for the "new" chat
      queryClient.setQueryData<TokenData>(["chatTokens", "new"], { tokens: [], chat_id: "" });
    }

    chatMutation.mutate({ prompt, chatId });
  };

  const handleQuestionClick = (question: string) => {
    sendMessage(question);
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2, height: "100%" }}>
      <ChatArea
        messages={history}
        input={userInput}
        handleInputChange={(e) => setUserInput(e.target.value)}
        handleSubmit={() => sendMessage()}
        isLoading={chatMutation.isLoading}
        messagesEndRef={messagesEndRef}
        onQuestionClick={handleQuestionClick}
      />
    </Box>
  );
};
export default EffiChat;
