import { Box } from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useChatMutation } from "src/customHooks/useChatMutation";
import ChatArea from "src/modules/Chat/components/ChatArea";
import { ChatMessage } from "src/services/api_definitions/chat.service";

// Define the TokenData type to match what we're storing in the cache
type TokenData = {
  tokens: string[];
  chat_id: string;
};

const EffiChat: React.FC = () => {
  const { selectedRole } = useAppSelector((state) => state.userManagement);
  const [chatId, setChatId] = useState<string | null>(null);
  const [userInput, setUserInput] = useState("");
  const [currentRole, setCurrentRole] = useState<string | null>(null);

  // Helper function to get role-specific storage keys
  const getStorageKeys = (role: string) => ({
    messages: `effi_chat_messages_${role}`,
    chatId: `effi_chat_id_${role}`,
  });

  // Helper function to clear session for a specific role
  const clearRoleSession = (role: string) => {
    const { messages, chatId: chatIdKey } = getStorageKeys(role);
    sessionStorage.removeItem(messages);
    sessionStorage.removeItem(chatIdKey);
  };

  // Initialize history from session storage based on current role
  const [history, setHistory] = useState<ChatMessage[]>(() => {
    if (!selectedRole) return [];

    try {
      const { messages } = getStorageKeys(selectedRole);
      const storedMessages = sessionStorage.getItem(messages);
      return storedMessages ? JSON.parse(storedMessages) : [];
    } catch (error) {
      console.error("Error loading chat messages from session storage:", error);
      return [];
    }
  });

  // token data lives in React-Query cache
  const { data: tokenData = { tokens: [], chat_id: "" } } = useQuery<TokenData>({
    queryKey: ["chatTokens", chatId || "new"],
    staleTime: Infinity, // never refetch automatically
    enabled: true, // always enabled to catch first response
    initialData: { tokens: [], chat_id: "" },
  });

  // Also query the "new" key to catch the first response when chatId is null
  const { data: newTokenData } = useQuery<TokenData>({
    queryKey: ["chatTokens", "new"],
    staleTime: Infinity,
    enabled: !chatId, // only enabled when chatId is null
    initialData: { tokens: [], chat_id: "" },
  });

  // Combine the data from both queries
  const effectiveTokenData = chatId ? tokenData : newTokenData;

  const chatMutation = useChatMutation();
  const queryClient = useQueryClient();

  // Watch for role changes and clear session if role changes
  useEffect(() => {
    if (selectedRole && currentRole && selectedRole !== currentRole) {
      console.log("Role changed from", currentRole, "to", selectedRole, "- clearing chat session");

      // Clear current session
      setHistory([]);
      setChatId(null);
      setUserInput("");

      // Clear query cache for the old role
      queryClient.removeQueries({ queryKey: ["chatTokens"] });

      // Load session for new role
      try {
        const { messages, chatId: chatIdKey } = getStorageKeys(selectedRole);
        const storedMessages = sessionStorage.getItem(messages);
        const storedChatId = sessionStorage.getItem(chatIdKey);

        if (storedMessages) {
          setHistory(JSON.parse(storedMessages));
        }
        if (storedChatId) {
          setChatId(storedChatId);
        }
      } catch (error) {
        console.error("Error loading chat session for new role:", error);
      }
    }

    // Update current role
    if (selectedRole) {
      setCurrentRole(selectedRole);
    }
  }, [selectedRole, currentRole, queryClient]);

  // Initialize chat ID from session storage for current role
  useEffect(() => {
    if (selectedRole && !chatId) {
      try {
        const { chatId: chatIdKey } = getStorageKeys(selectedRole);
        const storedChatId = sessionStorage.getItem(chatIdKey);
        if (storedChatId) {
          setChatId(storedChatId);
        }
      } catch (error) {
        console.error("Error loading chat ID from session storage:", error);
      }
    }
  }, [selectedRole, chatId]);

  // Watch for chat ID changes in the token data
  useEffect(() => {
    // Use effectiveTokenData to get the chat ID
    const currentTokenData = effectiveTokenData;

    if (currentTokenData.chat_id && (!chatId || chatId !== currentTokenData.chat_id)) {
      console.log("Setting chat ID from token data:", currentTokenData.chat_id);
      setChatId(currentTokenData.chat_id);
    }
  }, [effectiveTokenData.chat_id, chatId]);

  // every render, join tokens into the last assistant message
  useEffect(() => {
    // Use effectiveTokenData to get the tokens
    const currentTokenData = effectiveTokenData;

    if (!currentTokenData.tokens.length) return;

    // Log the token data for debugging
    console.log("Processing token data:", currentTokenData);

    const assistant = currentTokenData.tokens.join("");
    console.log("Joined assistant message:", assistant);

    setHistory((prev) => {
      const draft = [...prev];
      const last = draft[draft.length - 1];
      if (last?.role === "assistant") {
        console.log("Replacing last assistant message");
        draft.pop(); // overwrite
      } else {
        console.log("Adding new assistant message");
      }
      return [...draft, { role: "assistant", content: assistant }];
    });


  }, [effectiveTokenData.tokens]);

  // Add a ref to track the message container for auto-scrolling
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Persist messages to session storage whenever history changes (role-specific)
  useEffect(() => {
    if (!selectedRole) return;

    try {
      const { messages } = getStorageKeys(selectedRole);
      sessionStorage.setItem(messages, JSON.stringify(history));
    } catch (error) {
      console.error("Error saving chat messages to session storage:", error);
    }
  }, [history, selectedRole]);

  // Persist chat ID to session storage whenever it changes (role-specific)
  useEffect(() => {
    if (!selectedRole || !chatId) return;

    try {
      const { chatId: chatIdKey } = getStorageKeys(selectedRole);
      sessionStorage.setItem(chatIdKey, chatId);
    } catch (error) {
      console.error("Error saving chat ID to session storage:", error);
    }
  }, [chatId, selectedRole]);

  // Auto-scroll to the bottom when messages change
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: "smooth",
          block: "end"
        });
      }
    };

    // Use a small delay to ensure content is rendered before scrolling
    const timeoutId = setTimeout(scrollToBottom, 100);

    return () => clearTimeout(timeoutId);
  }, [history]);

  const sendMessage = (customPrompt?: string) => {
    const prompt = customPrompt || userInput.trim();
    if (!prompt) return;

    setHistory((prev) => [...prev, { role: "user", content: prompt }]);
    setUserInput("");

    // Clear the tokens for the new response
    if (chatId) {
      // If we have a chat ID, clear the tokens for that chat
      queryClient.setQueryData<TokenData>(["chatTokens", chatId], (prev) => ({
        tokens: [],
        chat_id: prev?.chat_id || "",
      }));
    } else {
      // If we don't have a chat ID, clear the tokens for the "new" chat
      queryClient.setQueryData<TokenData>(["chatTokens", "new"], { tokens: [], chat_id: "" });
    }

    chatMutation.mutate({ prompt, chatId });
  };

  const handleQuestionClick = (question: string) => {
    sendMessage(question);
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2, height: "100%" }}>
      <ChatArea
        messages={history}
        input={userInput}
        handleInputChange={(e) => setUserInput(e.target.value)}
        handleSubmit={() => sendMessage()}
        isLoading={chatMutation.isLoading}
        messagesEndRef={messagesEndRef}
        onQuestionClick={handleQuestionClick}
      />
    </Box>
  );
};
export default EffiChat;
